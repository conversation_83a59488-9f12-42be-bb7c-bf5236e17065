<sql splitStatements="false">
    <![CDATA[
CREATE OR REPLACE FUNCTION sandf.fn_get_rate_sheet_multi_class_new(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_plan_id INTEGER;
    quote_record RECORD;
    quote_json JSONB;
    benefit_premiums JSONB;
    carrier_name TEXT;
    carrier_order INTEGER;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array TEXT[];

    -- Section data structures
    sections_map JSONB := '{}'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefit_values JSONB;
    section_key TEXT;

    -- Pagination variables
    MAX_SECTIONS_PER_PAGE INTEGER := 16;
    total_sections INTEGER;
    current_page_sections INTEGER := 0;
    current_page_sections_array JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    section_idx INTEGER;

    -- Multi-class support variables
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;
    class_number TEXT;

    -- Class grouping and legend variables
    class_to_letter_map JSONB := '{}'::jsonb;
    class_legend_array TEXT[] := ARRAY[]::TEXT[];
    letter_index INTEGER := 1;
    current_letter TEXT;

    -- Benefit processing variables
    key_name TEXT;
    val JSONB;
    subval JSONB;
    subkey_name TEXT;
    mapped_key_name TEXT;
    friendly_name TEXT;
    volume_text TEXT;
    volume_numeric NUMERIC;
    coverage_order TEXT[] := ARRAY['single', 'couple', 'family'];
    coverage_type TEXT;
    should_include_key BOOLEAN;

    -- Configuration from config table
    config_json JSONB;
    benefit_to_premium_map JSONB;
    friendly_name_map JSONB;

    -- Calculations variables
    calculations_array JSONB := '[]'::jsonb;
    calculation_obj JSONB;
    carrier_total_monthly NUMERIC;
    carrier_total_annual NUMERIC;
    lowest_monthly NUMERIC := NULL;
    lowest_annual NUMERIC := NULL;
    percentage_diff NUMERIC;
    calculation_idx INTEGER;

    -- Premium calculation variables
    monthly_premiums_map JSONB := '{}'::jsonb;
    annual_premiums_map JSONB := '{}'::jsonb;
    first_annual_premium NUMERIC;
    total_monthly_premium NUMERIC;
    total_annual_premium NUMERIC;
    premium_value NUMERIC;
    calc_obj JSONB;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;
    
BEGIN
    -- Get configuration from config table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract benefitToPremium mapping
    IF config_json IS NOT NULL THEN
        benefit_to_premium_map := config_json -> 'benefitToPremium';
        friendly_name_map := config_json -> 'friendlyNameMap';
    ELSE
        benefit_to_premium_map := '{}'::jsonb;
        friendly_name_map := '{}'::jsonb;
    END IF;

    -- Get plan_id from plan_uuid
    SELECT plan_id INTO v_plan_id
    FROM sandf.plan
    WHERE plan_uuid = plan_uuid_param::uuid;

    IF v_plan_id IS NULL THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
    END IF;

    -- Step 1: Detect employee classes for this plan
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));

    RAISE NOTICE 'Found % employee classes: %', employee_class_count, employee_classes;

    -- Check if we found any employee classes
    IF employee_class_count = 0 OR employee_classes IS NULL THEN
        RAISE NOTICE 'ERROR: No employee classes found for plan: %', plan_uuid_param;
        RAISE NOTICE 'Returning empty result structure';
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
    END IF;

    -- Step 1.5: Build class to letter mapping (A, B, C, etc.)
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        current_letter := chr(64 + letter_index); -- A=65, B=66, etc.
        class_to_letter_map := class_to_letter_map || jsonb_build_object(current_employee_class, current_letter);
        class_legend_array := array_append(class_legend_array, current_letter || ' - ' || current_employee_class);
        letter_index := letter_index + 1;
    END LOOP;

    RAISE NOTICE 'Class to letter mapping: %', class_to_letter_map;
    RAISE NOTICE 'Class legend: %', class_legend_array;

    -- Step 2: Build carrier order map for all employee classes
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        carrier_order_map := carrier_order_map || jsonb_build_object(
            carrier_name,
            jsonb_build_object('order', carrier_order)
        );
    END LOOP;

    -- Step 3: Build ordered carriers array and capture first carrier name
    SELECT array_agg(carriers.carrier_name ORDER BY (carrier_order_map -> carriers.carrier_name ->> 'order')::integer ASC)
    INTO ordered_carriers_array
    FROM (
        SELECT DISTINCT jsonb_object_keys(carrier_order_map) as carrier_name
    ) carriers;

    -- Capture the first carrier name for display purposes
    IF array_length(ordered_carriers_array, 1) > 0 THEN
        first_carrier_name := ordered_carriers_array[1];

        -- Replace first carrier with "Current" in the display array
        ordered_carriers_array[1] := 'Current';
    END IF;
    
    -- Step 4: Build sections with class grouping and premium matching
    -- First collect all section data by class, then group and combine matching premiums
    DECLARE
        class_sections_map JSONB := '{}'::jsonb;  -- Store sections by class
        combined_sections_map JSONB := '{}'::jsonb;  -- Store final combined sections
        section_signature TEXT;  -- For matching premiums across classes
        matching_classes TEXT[];  -- Classes with matching premiums
        combined_class_name TEXT;  -- Final className for section
    BEGIN

    -- Process each employee class and collect section data
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;
        current_letter := class_to_letter_map ->> current_employee_class;

        -- Extract class number from employee class name
        -- Examples: " Class 1 - Salaried EE's" -> "1", " Class 2 - Hourly EE's" -> "2"
        class_number := CASE
            WHEN current_employee_class ~ 'Class [0-9]+' THEN
                regexp_replace(current_employee_class, '.*Class ([0-9]+).*', '\1')
            ELSE
                '1'  -- Default to 1 if no class number found
        END;

        RAISE NOTICE 'Processing employee class: % (class number: %, letter: %)', current_employee_class, class_number, current_letter;

        -- Initialize sections map for this class if not exists
        IF NOT class_sections_map ? current_employee_class THEN
            class_sections_map := class_sections_map || jsonb_build_object(current_employee_class, '{}'::jsonb);
        END IF;

        -- Process all quotes for this employee class to populate section values
        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description as carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
            ORDER BY
                COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
                c.description ASC
        LOOP
            quote_json := quote_record.formatted_quote_details::jsonb;
            benefit_premiums := quote_json -> 'benefitPremiums';
            carrier_name := quote_record.carrier_description;

            RAISE NOTICE 'Processing carrier: % for class: %', carrier_name, current_employee_class;

            -- Process each benefit premium (following original rate-sheet.sql pattern)
            FOR key_name, val IN
                SELECT j.k1, j.v1
                FROM jsonb_each(benefit_premiums) AS j(k1, v1)
            LOOP
                -- Skip the pre-calculated total fields
                IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                    -- Get mapped key name from config
                    IF benefit_to_premium_map ? key_name THEN
                        mapped_key_name := benefit_to_premium_map ->> key_name;
                    ELSE
                        mapped_key_name := key_name;
                    END IF;

                    -- Check if key should be included based on includes/excludes parameters
                    should_include_key := FALSE;

                    IF array_length(includes_param, 1) IS NULL THEN
                        -- No includes filter, check excludes only
                        IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                            should_include_key := TRUE;
                        END IF;
                    ELSE
                        -- Includes filter exists, key must be in includes and not in excludes
                        IF mapped_key_name = ANY(includes_param) THEN
                            IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                                should_include_key := TRUE;
                            END IF;
                        END IF;
                    END IF;

                    -- Only process if key should be included
                    IF should_include_key THEN
                        -- Get friendly name from ui_field table using friendly field instead of name
                        SELECT uf.friendly INTO friendly_name
                        FROM sandf.ui_field uf
                        WHERE uf.name = key_name
                        LIMIT 1;

                        -- Fallback to config friendlyNameMap if not found in ui_field
                        IF friendly_name IS NULL THEN
                            IF friendly_name_map ? key_name THEN
                                friendly_name := friendly_name_map ->> key_name;
                            ELSE
                                friendly_name := initcap(replace(key_name, '_', ' '));
                            END IF;
                        END IF;

                        -- Check if this is a nested structure (like extendedHealth with single/couple/family)
                        IF val ? 'single' OR val ? 'couple' OR val ? 'family' THEN
                            -- Apply special logic for extended health and dental couple coverage using global function
                            IF (mapped_key_name = 'extendedHealth' OR mapped_key_name = 'dental') AND val ? 'couple' AND val ? 'family' THEN
                                DECLARE
                                    updated_couple_data_multi JSONB;
                                BEGIN
                                    updated_couple_data_multi := sandf.fn_apply_family_to_couple_coverage(
                                        mapped_key_name,
                                        val -> 'couple',
                                        val -> 'family'
                                    );

                                    -- Update the couple data if it was changed
                                    IF updated_couple_data_multi != val -> 'couple' THEN
                                        val := jsonb_set(val, ARRAY['couple'], updated_couple_data_multi);
                                        RAISE NOTICE 'Multi-class: Applied family values to couple coverage for %', mapped_key_name;
                                    END IF;
                                END;
                            END IF;

                            -- Handle nested premium structure (following original pattern)
                            FOREACH coverage_type IN ARRAY coverage_order
                            LOOP
                                IF val ? coverage_type THEN
                                    subval := val -> coverage_type;
                                    subkey_name := coverage_type;

                                    -- Create section key without class number for grouping (use original key_name, not mapped)
                                    section_key := key_name || '_' || subkey_name;

                                    -- Look for existing section in this class's sections
                                    section_obj := class_sections_map -> current_employee_class -> section_key;
                                    IF section_obj IS NOT NULL THEN
                                        benefit_values := section_obj->'values';
                                    ELSE
                                        benefit_values := '{}'::jsonb;
                                    END IF;

                                -- Convert volume to numeric, default to 0 if conversion fails
                                BEGIN
                                    volume_text := COALESCE(subval->>'volume', '0');
                                    volume_numeric := sandf.safe_parse_numeric(volume_text);
                                EXCEPTION WHEN OTHERS THEN
                                    volume_numeric := 0;
                                END;

                                -- Handle string/numeric premium values properly for display
                                DECLARE
                                    display_rate TEXT;
                                    display_premium TEXT;
                                    raw_premium_val TEXT;
                                    raw_rate_val TEXT;
                                    premium_numeric NUMERIC;
                                    rate_numeric NUMERIC;
                                BEGIN
                                    -- Get raw values
                                    raw_premium_val := COALESCE(subval->>'premium', '-');
                                    raw_rate_val := COALESCE(subval->>'rate', '-');

                                    RAISE NOTICE 'Raw values - Rate: %, Premium: %', raw_rate_val, raw_premium_val;

                                    -- Format premium with proper rounding and comma formatting
                                    IF raw_premium_val = '-' THEN
                                        display_premium := '-';
                                    ELSE
                                        BEGIN
                                            premium_numeric := sandf.safe_parse_numeric(raw_premium_val);
                                            display_premium := '$' || to_char(ROUND(premium_numeric, 2), 'FM999,999,990.00');
                                        EXCEPTION WHEN OTHERS THEN
                                            display_premium := '$0.00';
                                        END;
                                    END IF;

                                    -- Format rate with proper rounding and comma formatting
                                    IF raw_rate_val = '-' THEN
                                        display_rate := '-';
                                    ELSE
                                        BEGIN
                                            rate_numeric := sandf.safe_parse_numeric(raw_rate_val);
                                            display_rate := '$' || to_char(ROUND(rate_numeric, 2), 'FM999,999,990.00');
                                        EXCEPTION WHEN OTHERS THEN
                                            display_rate := '$0.00';
                                        END;
                                    END IF;

                                    RAISE NOTICE 'Display values - Rate: %, Premium: %', display_rate, display_premium;

                                    -- Use "Current" for first carrier, otherwise use actual name
                                    IF carrier_name = first_carrier_name THEN
                                        display_carrier_name := 'Current';
                                    ELSE
                                        display_carrier_name := carrier_name;
                                    END IF;

                                    benefit_values := benefit_values || jsonb_build_object(
                                        display_carrier_name,
                                        jsonb_build_object(
                                            'rate', to_jsonb(display_rate),
                                            'volume', to_jsonb(volume_numeric),
                                            'premium', to_jsonb(display_premium)
                                        )
                                    );
                                END;

                                    -- Build section object (store in class sections map)
                                    section_obj := jsonb_build_object(
                                        'id', section_key,
                                        'name', friendly_name || ' ' || initcap(subkey_name),
                                        'values', benefit_values,
                                        'sort_key', key_name || '_' || subkey_name,
                                        'coverage_order', array_position(coverage_order, coverage_type),
                                        'base_benefit', key_name,
                                        'coverage_type', subkey_name,
                                        'class_number', class_number::integer,
                                        'employee_class', current_employee_class
                                    );

                                    -- Store section in class sections map
                                    class_sections_map := jsonb_set(
                                        class_sections_map,
                                        ARRAY[current_employee_class, section_key],
                                        section_obj
                                    );
                            END IF;
                        END LOOP;

                        ELSE
                            -- Handle direct premium values (following original pattern)
                            -- Create section key without class number for grouping (use original key_name, not mapped)
                            section_key := key_name;

                            -- Look for existing section in this class's sections
                            section_obj := class_sections_map -> current_employee_class -> section_key;
                            IF section_obj IS NOT NULL THEN
                                benefit_values := section_obj->'values';
                            ELSE
                                benefit_values := '{}'::jsonb;
                            END IF;

                        -- Convert volume to numeric, default to 0 if conversion fails
                        BEGIN
                            volume_text := COALESCE(val->>'volume', '0');
                            volume_numeric := sandf.safe_parse_numeric(volume_text);
                        EXCEPTION WHEN OTHERS THEN
                            volume_numeric := 0;
                        END;

                        -- Handle string/numeric premium values properly for display (direct values)
                        DECLARE
                            display_rate TEXT;
                            display_premium TEXT;
                            raw_premium_val TEXT;
                            raw_rate_val TEXT;
                            premium_numeric NUMERIC;
                            rate_numeric NUMERIC;
                        BEGIN
                            -- Get raw values
                            raw_premium_val := COALESCE(val->>'premium', '-');
                            raw_rate_val := COALESCE(val->>'rate', '-');

                            RAISE NOTICE 'Raw values (direct) - Rate: %, Premium: %', raw_rate_val, raw_premium_val;

                            -- Format premium with proper rounding and comma formatting
                            IF raw_premium_val = '-' THEN
                                display_premium := '-';
                            ELSE
                                BEGIN
                                    premium_numeric := sandf.safe_parse_numeric(raw_premium_val);
                                    display_premium := '$' || to_char(ROUND(premium_numeric, 2), 'FM999,999,990.00');
                                EXCEPTION WHEN OTHERS THEN
                                    display_premium := '$0.00';
                                END;
                            END IF;

                            -- Format rate with proper rounding and comma formatting
                            IF raw_rate_val = '-' THEN
                                display_rate := '-';
                            ELSE
                                BEGIN
                                    rate_numeric := sandf.safe_parse_numeric(raw_rate_val);
                                    display_rate := '$' || to_char(ROUND(rate_numeric, 2), 'FM999,999,990.00');
                                EXCEPTION WHEN OTHERS THEN
                                    display_rate := '$0.00';
                                END;
                            END IF;

                            RAISE NOTICE 'Display values (direct) - Rate: %, Premium: %', display_rate, display_premium;

                            -- Use "Current" for first carrier, otherwise use actual name
                            IF carrier_name = first_carrier_name THEN
                                display_carrier_name := 'Current';
                            ELSE
                                display_carrier_name := carrier_name;
                            END IF;

                            benefit_values := benefit_values || jsonb_build_object(
                                display_carrier_name,
                                jsonb_build_object(
                                    'rate', to_jsonb(display_rate),
                                    'volume', to_jsonb(volume_numeric),
                                    'premium', to_jsonb(display_premium)
                                )
                            );
                        END;

                            -- Build section object (store in class sections map)
                            section_obj := jsonb_build_object(
                                'id', section_key,
                                'name', friendly_name,
                                'values', benefit_values,
                                'sort_key', key_name,
                                'base_benefit', key_name,
                                'coverage_type', '',
                                'coverage_order', 0,
                                'class_number', class_number::integer,
                                'employee_class', current_employee_class
                            );

                            -- Store section in class sections map
                            class_sections_map := jsonb_set(
                                class_sections_map,
                                ARRAY[current_employee_class, section_key],
                                section_obj
                            );
                    END IF;
                    END IF; -- End should_include_key check
                END IF;
            END LOOP;
        END LOOP;
    END LOOP;

    -- Step 4.5: Group sections by matching premiums and create final sections array
    DECLARE
        all_section_keys TEXT[];
        section_key_item TEXT;
        class_section JSONB;
        signature_to_classes JSONB := '{}'::jsonb;
        signature_to_section JSONB := '{}'::jsonb;
        processed_signatures TEXT[] := ARRAY[]::TEXT[];
    BEGIN
        -- Get all unique section keys across all classes
        SELECT array_agg(DISTINCT subq.section_key)
        INTO all_section_keys
        FROM (
            SELECT jsonb_object_keys(class_data.class_sections) as section_key
            FROM jsonb_each(class_sections_map) as class_data(class_name, class_sections)
        ) subq;

        RAISE NOTICE 'All section keys: %', all_section_keys;

        -- Check if we have any section keys to process
        IF all_section_keys IS NULL OR array_length(all_section_keys, 1) IS NULL THEN
            RAISE NOTICE 'No section keys found, skipping section grouping';
        ELSE
            -- For each section key, group classes with matching premium values
            FOREACH section_key_item IN ARRAY all_section_keys
            LOOP
            signature_to_classes := '{}'::jsonb;
            signature_to_section := '{}'::jsonb;

            -- Check each class for this section key
            FOR current_employee_class IN
                SELECT jsonb_object_keys(class_sections_map)
            LOOP
                class_section := class_sections_map -> current_employee_class -> section_key_item;

                IF class_section IS NOT NULL THEN
                    -- Create signature from premium values for matching
                    section_signature := '';
                    FOR carrier_name IN
                        SELECT jsonb_object_keys(class_section -> 'values')
                    LOOP
                        section_signature := section_signature || carrier_name || ':' ||
                            COALESCE(class_section -> 'values' -> carrier_name ->> 'rate', '') || '|' ||
                            COALESCE(class_section -> 'values' -> carrier_name ->> 'volume', '') || '|' ||
                            COALESCE(class_section -> 'values' -> carrier_name ->> 'premium', '') || ';';
                    END LOOP;

                    -- Group classes by signature
                    IF signature_to_classes ? section_signature THEN
                        signature_to_classes := jsonb_set(
                            signature_to_classes,
                            ARRAY[section_signature],
                            (signature_to_classes -> section_signature) || jsonb_build_array(current_employee_class)
                        );
                    ELSE
                        signature_to_classes := signature_to_classes || jsonb_build_object(
                            section_signature,
                            jsonb_build_array(current_employee_class)
                        );
                        signature_to_section := signature_to_section || jsonb_build_object(
                            section_signature,
                            class_section
                        );
                    END IF;
                END IF;
            END LOOP;

            -- Create combined sections for each signature
            FOR section_signature IN
                SELECT jsonb_object_keys(signature_to_classes)
            LOOP
                matching_classes := ARRAY(SELECT jsonb_array_elements_text(signature_to_classes -> section_signature));
                section_obj := signature_to_section -> section_signature;

                -- Build className based on matching classes
                IF array_length(matching_classes, 1) = employee_class_count THEN
                    combined_class_name := 'ALL';
                ELSE
                    -- Convert class names to letters and join
                    DECLARE
                        class_letters TEXT[] := ARRAY[]::TEXT[];
                        class_item TEXT;
                    BEGIN
                        IF matching_classes IS NOT NULL AND array_length(matching_classes, 1) > 0 THEN
                            FOREACH class_item IN ARRAY matching_classes
                            LOOP
                                class_letters := array_append(class_letters, class_to_letter_map ->> class_item);
                            END LOOP;
                            combined_class_name := array_to_string(class_letters, ',');
                        ELSE
                            combined_class_name := 'UNKNOWN';
                        END IF;
                    END;
                END IF;

                -- Create final section with className and proper ID
                section_obj := jsonb_set(section_obj, ARRAY['className'], to_jsonb(combined_class_name));
                section_obj := jsonb_set(section_obj, ARRAY['id'], to_jsonb(section_obj ->> 'id' ||
                    CASE
                        WHEN combined_class_name = 'ALL' THEN ''
                        ELSE replace(combined_class_name, ',', '')
                    END));

                -- Enhanced empty section detection following plan-design-ungroup-multi.sql pattern
                DECLARE
                    section_has_data BOOLEAN := FALSE;
                    carrier_item TEXT;
                    carrier_rate TEXT;
                    carrier_premium TEXT;
                    all_carriers_empty BOOLEAN := TRUE;
                    carrier_count INTEGER := 0;
                    empty_carrier_count INTEGER := 0;
                BEGIN
                    -- Count total carriers and check each one
                    FOR carrier_item IN SELECT jsonb_object_keys(section_obj -> 'values')
                    LOOP
                        carrier_count := carrier_count + 1;
                        carrier_rate := section_obj -> 'values' -> carrier_item ->> 'rate';
                        carrier_premium := section_obj -> 'values' -> carrier_item ->> 'premium';

                        -- Check if this carrier has actual data (not null, empty, or '-')
                        IF (carrier_rate IS NOT NULL AND trim(carrier_rate) != '' AND carrier_rate != '-') OR
                           (carrier_premium IS NOT NULL AND trim(carrier_premium) != '' AND carrier_premium != '-') THEN
                            section_has_data := TRUE;
                            all_carriers_empty := FALSE;
                        ELSE
                            empty_carrier_count := empty_carrier_count + 1;
                        END IF;
                    END LOOP;

                    -- Only add section if it has actual data and not all carriers are empty
                    -- Following the pattern: hide sections where all carriers show '-'
                    IF section_has_data AND NOT all_carriers_empty THEN
                        -- Remove metadata fields and add to final sections array
                        section_obj := section_obj - 'employee_class' - 'class_number';
                        sections_array := sections_array || section_obj;

                        RAISE NOTICE 'Added section % with data (% carriers, % empty)',
                            section_obj ->> 'id', carrier_count, empty_carrier_count;
                    ELSE
                        RAISE NOTICE 'Skipped empty section % (% carriers, % empty)',
                            section_obj ->> 'id', carrier_count, empty_carrier_count;
                    END IF;
                END;
            END LOOP;
        END LOOP;
        END IF; -- End of null check for all_section_keys
    END;
    END; -- End of Step 4 DECLARE block

    -- Step 5: Order sections like original rate-sheet.sql
    DECLARE
        temp_sections_array JSONB := '[]'::jsonb;
        ordered_section RECORD;
        class_order_value INTEGER;
    BEGIN
        -- Order sections by display_order from ui_field table, then by class letter order, then by coverage order
        FOR ordered_section IN
            SELECT
                elem as section_data,
                COALESCE(uf.display_order, 999999) as sort_order,
                elem->>'sort_key' as section_key,
                COALESCE((elem->>'coverage_order')::INT, 999) as coverage_sort,
                elem->>'base_benefit' as base_benefit_name,
                elem->>'coverage_type' as coverage_type_name,
                elem->>'className' as class_name
            FROM jsonb_array_elements(sections_array) elem
            LEFT JOIN sandf.ui_field uf ON uf.name = COALESCE(elem->>'base_benefit',
                CASE
                    WHEN elem->>'sort_key' LIKE '%_%' THEN
                        split_part(elem->>'sort_key', '_', 1)
                    ELSE
                        elem->>'sort_key'
                END)
            ORDER BY
                sort_order ASC,
                base_benefit_name ASC,
                coverage_sort ASC,
                -- Order by class name alphabetically: ALL first, then by first letter (A,C,D before B)
                CASE
                    WHEN elem->>'className' = 'ALL' THEN 0
                    ELSE ascii(substring(elem->>'className', 1, 1)) - 64  -- Sort by first letter: A=1, B=2, etc.
                END ASC,
                elem->>'className' ASC,
                section_key ASC
        LOOP
            -- Remove metadata fields like original (keep only id, name, values, className)
            section_obj := ordered_section.section_data - 'sort_key' - 'coverage_order' - 'base_benefit' - 'coverage_type';
            temp_sections_array := temp_sections_array || section_obj;
        END LOOP;
        sections_array := temp_sections_array;
    END;

    -- Step 6: Build calculations following cutting-to-chase-multi.sql pattern
    -- Reset calculation variables
    monthly_premiums_map := '{}'::jsonb;
    annual_premiums_map := '{}'::jsonb;
    calculations_array := '[]'::jsonb;

    RAISE NOTICE 'Starting calculations following cutting-to-chase-multi.sql pattern';
    RAISE NOTICE 'Will sum individual class premiums for each carrier across all % employee classes', employee_class_count;

    -- Following cutting-to-chase-multi.sql: Process all employee classes to sum premiums per carrier
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        RAISE NOTICE 'Processing employee class: %', current_employee_class;

        -- Get all quotes for this employee class and calculate totals (ordered by user preference)
        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description AS carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
            ORDER BY sandf.get_user_preference_order(
                user_id_param,
                plan_uuid_param,
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC, c.description ASC
        LOOP
            carrier_name := quote_record.carrier_description;

            -- Extract benefit premiums from formatted_quote_details
            benefit_premiums := quote_record.formatted_quote_details -> 'benefitPremiums';

            -- Process benefit premiums if found
            IF benefit_premiums IS NOT NULL AND jsonb_typeof(benefit_premiums) = 'object' THEN
                -- Manual calculation like cutting-to-chase-multi.sql (sum all individual premiums)
                total_monthly_premium := 0;

                RAISE NOTICE 'Starting manual calculation for carrier % class %', carrier_name, current_employee_class;

                -- Sum all premiums for this carrier in this class (excluding pre-calculated totals)
                FOR key_name, val IN
                    SELECT j.k1, j.v1
                    FROM jsonb_each(benefit_premiums) AS j(k1, v1)
                LOOP
                    IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                        -- Get mapped key name from config
                        IF benefit_to_premium_map ? key_name THEN
                            mapped_key_name := benefit_to_premium_map ->> key_name;
                        ELSE
                            mapped_key_name := key_name;
                        END IF;

                        -- Check if key should be included based on includes/excludes parameters
                        should_include_key := FALSE;

                        IF array_length(includes_param, 1) IS NULL THEN
                            -- No includes filter, check excludes only
                            IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                                should_include_key := TRUE;
                            END IF;
                        ELSE
                            -- Includes filter exists, key must be in includes and not in excludes
                            IF mapped_key_name = ANY(includes_param) THEN
                                IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                                    should_include_key := TRUE;
                                END IF;
                            END IF;
                        END IF;

                        IF should_include_key THEN
                            -- Check if nested structure (single/couple/family)
                            IF val ? 'single' OR val ? 'couple' OR val ? 'family' THEN
                                -- Apply special logic for extended health and dental couple coverage using global function
                                IF (mapped_key_name = 'extendedHealth' OR mapped_key_name = 'dental') AND val ? 'couple' AND val ? 'family' THEN
                                    DECLARE
                                        updated_couple_data_rate_multi JSONB;
                                    BEGIN
                                        updated_couple_data_rate_multi := sandf.fn_apply_family_to_couple_coverage(
                                            mapped_key_name,
                                            val -> 'couple',
                                            val -> 'family'
                                        );

                                        -- Update the couple data if it was changed
                                        IF updated_couple_data_rate_multi != val -> 'couple' THEN
                                            val := jsonb_set(val, ARRAY['couple'], updated_couple_data_rate_multi);
                                            RAISE NOTICE 'Rate-sheet Multi-class: Applied family values to couple coverage for %', mapped_key_name;
                                        END IF;
                                    END;
                                END IF;

                                FOREACH coverage_type IN ARRAY coverage_order
                                LOOP
                                    IF val ? coverage_type THEN
                                        subval := val -> coverage_type;
                                        IF subval ? 'premium' THEN
                                            premium_value := sandf.safe_parse_numeric(subval ->> 'premium');
                                            total_monthly_premium := total_monthly_premium + premium_value;
                                            RAISE NOTICE 'Added nested: Class %, Benefit %, Coverage %, Premium: %',
                                                current_employee_class, key_name, coverage_type, premium_value;
                                        END IF;
                                    END IF;
                                END LOOP;
                            ELSE
                                -- Direct premium value
                                IF val ? 'premium' THEN
                                    premium_value := sandf.safe_parse_numeric(val ->> 'premium');
                                    total_monthly_premium := total_monthly_premium + premium_value;
                                    RAISE NOTICE 'Added direct: Class %, Benefit %, Premium: %',
                                        current_employee_class, key_name, premium_value;
                                END IF;
                            END IF;
                        END IF;
                    END IF;
                END LOOP;

                RAISE NOTICE 'Finished manual calculation for carrier % class %: total = %',
                    carrier_name, current_employee_class, total_monthly_premium;

                -- Add to carrier totals (sum across all employee classes) - following cutting-to-chase pattern
                IF monthly_premiums_map ? carrier_name THEN
                    -- Add to existing total
                    DECLARE
                        previous_total NUMERIC := (monthly_premiums_map ->> carrier_name)::NUMERIC;
                        new_total NUMERIC := previous_total + total_monthly_premium;
                    BEGIN
                        monthly_premiums_map := jsonb_set(
                            monthly_premiums_map,
                            ARRAY[carrier_name],
                            to_jsonb(new_total)
                        );
                        annual_premiums_map := jsonb_set(
                            annual_premiums_map,
                            ARRAY[carrier_name],
                            to_jsonb(new_total * 12)
                        );
                        RAISE NOTICE 'Updated carrier % total: % + % = %',
                            carrier_name, previous_total, total_monthly_premium, new_total;
                    END;
                ELSE
                    -- Initialize total for this carrier
                    monthly_premiums_map := monthly_premiums_map || jsonb_build_object(
                        carrier_name, total_monthly_premium
                    );
                    annual_premiums_map := annual_premiums_map || jsonb_build_object(
                        carrier_name, total_monthly_premium * 12
                    );
                    RAISE NOTICE 'Initialized carrier % total: %', carrier_name, total_monthly_premium;
                END IF;
            ELSE
                RAISE NOTICE 'No benefit premiums found for carrier % class %', carrier_name, current_employee_class;
            END IF;
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Final monthly premiums map (sum of ALL classes): %', monthly_premiums_map::TEXT;
    RAISE NOTICE 'Final annual premiums map (sum of ALL classes): %', annual_premiums_map::TEXT;

    -- Prepare calculations array with differences and percentages (formatted) in user-preferred order
    -- Note: These calculations are based on summing ALL individual class premiums (ignoring rollup)
    IF array_length(ordered_carriers_array, 1) > 0 THEN
        first_annual_premium := COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> ordered_carriers_array[1]), 0);
        RAISE NOTICE 'First carrier: %, First annual premium (sum of ALL classes): %', ordered_carriers_array[1], first_annual_premium;
        -- Build calculation objects for each carrier using totals from ALL individual classes
        FOREACH carrier_name IN ARRAY ordered_carriers_array LOOP
            DECLARE
                carrier_monthly NUMERIC := COALESCE(sandf.safe_parse_numeric(monthly_premiums_map ->> carrier_name), 0);
                carrier_annual NUMERIC := COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0);
                dollar_diff TEXT;
                percentage_diff_text TEXT;
            BEGIN
                RAISE NOTICE 'Building calculation for carrier: %, Monthly (sum ALL classes): %, Annual (sum ALL classes): %',
                    carrier_name, carrier_monthly, carrier_annual;

                -- Use "Current" for first carrier, otherwise use actual name
                IF carrier_name = first_carrier_name THEN
                    display_carrier_name := 'Current';
                ELSE
                    display_carrier_name := carrier_name;
                END IF;

                -- Calculate dollar difference
                IF carrier_name = first_carrier_name THEN
                    dollar_diff := '$0.00'::TEXT;
                ELSIF carrier_annual = 0 THEN
                    dollar_diff := '$0.00'::TEXT;
                ELSE
                    dollar_diff := '$' || to_char(ROUND(first_annual_premium - carrier_annual, 2), 'FM999,999,990.00');
                END IF;

                -- Calculate percentage difference
                IF carrier_name = first_carrier_name THEN
                    percentage_diff_text := '0.00%'::TEXT;
                ELSIF carrier_annual = 0 OR first_annual_premium = 0 THEN
                    percentage_diff_text := '0.00%'::TEXT;
                ELSE
                    percentage_diff_text := to_char(((carrier_annual - first_annual_premium) / NULLIF(first_annual_premium, 0)) * 100, 'FM999990.00') || '%';
                END IF;

                RAISE NOTICE 'Calculated differences - Dollar: %, Percentage: %', dollar_diff, percentage_diff_text;

                calc_obj := jsonb_build_object(
                    'carrier', display_carrier_name,
                    'totalMonthlyPremiums', '$' || to_char(ROUND(carrier_monthly, 2), 'FM999,999,990.00'),
                    'annualPremium', '$' || to_char(ROUND(carrier_annual, 2), 'FM999,999,990.00'),
                    '$ Difference From #1', dollar_diff,
                    'Percentage Different From #1', percentage_diff_text
                );

                RAISE NOTICE 'Created calculation object: %', calc_obj::TEXT;
                calculations_array := calculations_array || calc_obj;
            END;
        END LOOP;
    END IF;

    RAISE NOTICE 'Final calculations array (sum of ALL classes): %', calculations_array::TEXT;
    RAISE NOTICE 'Total calculations count: %', jsonb_array_length(calculations_array);
    RAISE NOTICE 'Calculations completed by summing ALL individual class premiums - rollup logic NOT applied to totals';



    -- Ensure sections_array is not null and count total sections
    IF sections_array IS NULL THEN
        sections_array := '[]'::jsonb;
    END IF;

    total_sections := jsonb_array_length(sections_array);

    RAISE NOTICE 'Total sections found: %', total_sections;

    -- Step 7: Handle pagination following rate-sheet-new-data-v2.json structure
    -- If total sections <= MAX_SECTIONS_PER_PAGE, put all sections in first object and calculations in second
    IF total_sections <= MAX_SECTIONS_PER_PAGE THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', sections_array
            ),
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', '[]'::jsonb,
                'classLegend', class_legend_array,
                'calculations', calculations_array
            )
        );
    END IF;

    -- Otherwise, paginate by redistributing sections across pages
    current_page_sections := 0;
    current_page_sections_array := '[]'::jsonb;

    -- Only run pagination loop if we have sections
    IF total_sections > 0 THEN
        FOR section_idx IN 0..(total_sections-1) LOOP
            current_page_sections_array := current_page_sections_array || jsonb_build_array(sections_array -> section_idx);
            current_page_sections := current_page_sections + 1;

            -- Check if we've reached the page limit
            IF current_page_sections >= MAX_SECTIONS_PER_PAGE THEN
                result_pages := result_pages || jsonb_build_array(
                    jsonb_build_object(
                        'carriers', ordered_carriers_array,
                        'sections', current_page_sections_array
                    )
                );

                -- Reset for next page
                current_page_sections_array := '[]'::jsonb;
                current_page_sections := 0;
            END IF;
        END LOOP;
    END IF;

    -- Add any remaining sections as the last page with calculations and classLegend
    IF jsonb_array_length(current_page_sections_array) > 0 THEN
        result_pages := result_pages || jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', current_page_sections_array,
                'classLegend', class_legend_array,
                'calculations', calculations_array
            )
        );
    ELSE
        -- If no remaining sections, create a final page with just calculations and classLegend
        result_pages := result_pages || jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', '[]'::jsonb,
                'classLegend', class_legend_array,
                'calculations', calculations_array
            )
        );
    END IF;

    RETURN result_pages;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in rate sheet v2 function: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END;
$$;
]]>
        </sql>
